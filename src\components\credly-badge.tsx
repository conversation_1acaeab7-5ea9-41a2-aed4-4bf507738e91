interface CredlyBadgeProps {
  badgeId: string
  width?: number
  height?: number
}

export function CredlyBadge({ badgeId, width = 150, height = 270 }: CredlyBadgeProps) {

  return (
    <div className="relative">
      {/* Use direct iframe embed - this method works reliably */}
      <iframe
        src={`https://www.credly.com/embedded_badge/${badgeId}`}
        width={width}
        height={height}
        frameBorder="0"
        scrolling="no"
        className="rounded-lg shadow-sm"
        title="Credly Badge"
        loading="lazy"
      />

      {/* Fallback for no-script environments */}
      <noscript>
        <div
          className="flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-lg border-2 border-blue-200 dark:border-blue-800 shadow-sm"
          style={{ width: `${width}px`, height: `${height}px` }}
        >
          <div className="text-center p-4 space-y-3">
            <div className="w-12 h-12 mx-auto bg-blue-600 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <p className="font-bold text-sm text-blue-900 dark:text-blue-100 mb-1">
                ISC2 Certified in Cybersecurity
              </p>
              <p className="text-xs text-blue-700 dark:text-blue-300 mb-2">
                (CC) Professional Certification
              </p>
            </div>
            <a
              href={`https://www.credly.com/badges/${badgeId}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 hover:underline transition-colors"
            >
              View on Credly
              <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </a>
          </div>
        </div>
      </noscript>
    </div>
  )
}
