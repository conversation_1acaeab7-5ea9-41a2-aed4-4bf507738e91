import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { ThemeToggle } from "@/components/theme-toggle"
import { SocialLinks } from "@/components/social-links"
import { CredlyBadge } from "@/components/credly-badge"
import { TextRollBasic } from "@/components/core/text-roll"

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Skip to main content link for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50"
      >
        Skip to main content
      </a>

      {/* Header with Theme Toggle */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto flex h-14 max-w-screen-xl items-center justify-between px-4">
          <h1 className="text-lg font-semibold text-foreground"><PERSON></h1>
          <ThemeToggle />
        </div>
      </header>

      <main id="main-content" className="container mx-auto max-w-4xl px-4 py-8 space-y-12">
        {/* Hero Section */}
        <section className="text-center space-y-6 animate-in fade-in duration-1000" aria-labelledby="hero-heading">
          <div className="space-y-4">
            <h1 id="hero-heading" className="font-bold tracking-tight sm:text-5xl md:text-6xl">
              <TextRollBasic />
            </h1>
            <div className="mx-auto w-32 h-32 rounded-full overflow-hidden border-2 border-border bg-muted">
              <Image
                src="/profile-placeholder.svg"
                alt="Arthur Locke profile photo placeholder"
                width={128}
                height={128}
                className="w-full h-full object-cover"
                priority
              />
            </div>
          </div>
        </section>

        {/* Social Media Section */}
        <section className="space-y-6" aria-labelledby="social-heading">
          <h2 id="social-heading" className="text-3xl font-bold text-center">Connect With Me</h2>
          <div className="flex justify-center">
            <SocialLinks />
          </div>
        </section>

        {/* Certifications Section */}
        <section className="space-y-6" aria-labelledby="certifications-heading">
          <h2 id="certifications-heading" className="text-3xl font-bold text-center">Certifications</h2>
          <Card className="mx-auto max-w-md hover:shadow-lg transition-shadow duration-200">
            <CardContent className="flex justify-center pt-6">
              <CredlyBadge badgeId="2054310e-e8ff-4b2d-8fa7-ab33a975c32c" />
            </CardContent>
          </Card>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/50" role="contentinfo">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          <div className="space-y-4 text-center text-sm text-muted-foreground">
            <div className="space-y-2">
              <h3 className="font-semibold text-foreground">Business Information</h3>
              <address className="not-italic space-y-1">
                <div>
                  <span className="font-medium">IČO:</span> 14282721
                </div>
                <div>
                  <span className="font-medium">Address:</span> Oblouková 1254/6, 101 00, Praha 10 – Vršovice
                </div>
                <div>
                  <span className="font-medium">Email:</span>{" "}
                  <a
                    href="mailto:<EMAIL>"
                    className="hover:underline text-primary"
                    aria-label="Send email to Arthur Locke"
                  >
                    <EMAIL>
                  </a>
                </div>
              </address>
            </div>
            <div className="pt-4 border-t">
              <p>&copy; {new Date().getFullYear()} Arthur Locke. All rights reserved.</p>
            </div>
          </div>
        </div>
      </footer>

    </div>
  )
}
