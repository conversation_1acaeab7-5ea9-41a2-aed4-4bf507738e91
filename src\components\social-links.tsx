import { Github, Linkedin, Mail, Twitter } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

const socialLinks = [
  {
    name: "GitHub",
    url: "https://github.com/A-Locke",
    icon: Github,
    label: "Visit <PERSON>'s GitHub profile",
  },
  {
    name: "LinkedIn",
    url: "https://www.linkedin.com/in/arthur-locke/",
    icon: Linkedin,
    label: "Connect with Arthur on LinkedIn",
  },
  {
    name: "X (Twitter)",
    url: "https://x.com/IamArthurLocke",
    icon: Twitter,
    label: "Follow Arthur on X (Twitter)",
  },
  {
    name: "Email",
    url: "mailto:<EMAIL>",
    icon: Mail,
    label: "Send an email to <PERSON>",
  },
]

export function SocialLinks() {
  return (
    <div className="flex flex-wrap gap-4 justify-center sm:justify-start">
      {socialLinks.map((link) => {
        const Icon = link.icon
        return (
          <Button
            key={link.name}
            variant="outline"
            size="sm"
            asChild
            className="gap-2"
          >
            <a
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={link.label}
            >
              <Icon className="h-4 w-4" />
              {link.name}
            </a>
          </Button>
        )
      })}
    </div>
  )
}
